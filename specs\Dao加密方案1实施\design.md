# 技术方案设计

## 架构概述

基于现有的EncryptedClassLoader和EncryptedBeanRegistrar机制，扩展实现自定义MyBatis扫描器，使其能够处理加密的Dao接口。

## 技术栈

- **Spring Boot 2.6.4** - 主框架
- **MyBatis 2.2.2** - ORM框架  
- **EncryptedClassLoader** - 自定义类加载器
- **AES加密** - 类文件加密算法
- **Maven** - 构建工具

## 核心组件设计

### 1. EncryptedMapperScanner

自定义MyBatis扫描器，负责扫描和注册加密的Dao接口。

```java
@Component
public class EncryptedMapperScanner implements BeanDefinitionRegistryPostProcessor {
    
    private static final String ENCRYPTED_DAO_PACKAGE = "com.example.secure.dao";
    
    @Override
    public void postProcessBeanDefinitionRegistry(BeanDefinitionRegistry registry) {
        // 1. 创建EncryptedClassLoader
        // 2. 扫描加密的Dao接口
        // 3. 为每个接口创建MapperFactoryBean
        // 4. 注册到Spring容器
    }
}
```

### 2. 扫描流程设计

```mermaid
flowchart TD
    A[系统启动] --> B[EncryptedMapperScanner启动]
    B --> C[创建EncryptedClassLoader]
    C --> D[扫描encrypted/com/example/secure/dao目录]
    D --> E[发现加密的.class文件]
    E --> F[使用EncryptedClassLoader加载接口]
    F --> G[创建MapperFactoryBean]
    G --> H[注册到Spring容器]
    H --> I[MyBatis代理创建完成]
```

### 3. 类加载策略

| 组件 | 加载器 | 说明 |
|------|--------|------|
| TestDao接口 | EncryptedClassLoader | 加密保护，通过自定义扫描器加载 |
| TestEntity | EncryptedClassLoader | 加密保护，已有机制支持 |
| TestMapper.xml | EncryptedResourceLoader | 加密保护，通过自定义资源加载器 |
| Service层 | EncryptedClassLoader | 加密保护，已有机制支持 |

## 详细实现设计

### 1. 修改主启动类

移除@MapperScan注解，避免与自定义扫描器冲突：

```java
@SpringBootApplication
// @MapperScan("com.example.secure.dao") // 移除此注解
public class EncryptedDemoApplication {
    // 保持现有启动逻辑
}
```

### 2. EncryptedMapperScanner实现

```java
@Slf4j
@Component
public class EncryptedMapperScanner implements BeanDefinitionRegistryPostProcessor {

    @Override
    public void postProcessBeanDefinitionRegistry(BeanDefinitionRegistry registry) {
        try {
            // 创建EncryptedClassLoader
            EncryptedClassLoader encryptedLoader = createEncryptedClassLoader();

            // 扫描加密的Dao接口
            List<String> daoClasses = scanEncryptedDaoClasses();

            // 注册每个Dao接口
            for (String className : daoClasses) {
                registerMapperBean(registry, encryptedLoader, className);
            }

        } catch (Exception e) {
            log.error("Failed to scan encrypted mappers", e);
        }
    }

    private void registerMapperBean(BeanDefinitionRegistry registry,
                                  EncryptedClassLoader loader, String className) {
        // 使用EncryptedClassLoader加载接口
        Class<?> mapperInterface = loader.loadClass(className);

        // 创建自定义MapperFactoryBean，支持加密XML
        BeanDefinition beanDefinition = BeanDefinitionBuilder
            .genericBeanDefinition(EncryptedMapperFactoryBean.class)
            .addPropertyValue("mapperInterface", mapperInterface)
            .addPropertyValue("sqlSessionFactory", sqlSessionFactory)
            .addPropertyValue("encryptedClassLoader", loader)
            .getBeanDefinition();

        // 注册到Spring容器
        String beanName = generateBeanName(className);
        registry.registerBeanDefinition(beanName, beanDefinition);
    }
}
```

### 3. EncryptedMapperFactoryBean实现

```java
@Slf4j
public class EncryptedMapperFactoryBean<T> extends MapperFactoryBean<T> {

    private EncryptedClassLoader encryptedClassLoader;

    public void setEncryptedClassLoader(EncryptedClassLoader encryptedClassLoader) {
        this.encryptedClassLoader = encryptedClassLoader;
    }

    @Override
    protected void checkDaoConfig() {
        super.checkDaoConfig();

        // 设置自定义的资源加载器，用于加载加密的XML文件
        if (getSqlSession() != null) {
            Configuration configuration = getSqlSession().getConfiguration();
            if (configuration instanceof DefaultConfiguration) {
                ((DefaultConfiguration) configuration).setResourceLoader(
                    new EncryptedResourceLoader(encryptedClassLoader));
            }
        }
    }
}
```

### 4. EncryptedResourceLoader实现

```java
@Slf4j
public class EncryptedResourceLoader implements ResourceLoader {

    private final EncryptedClassLoader encryptedClassLoader;
    private final DefaultResourceLoader defaultResourceLoader;

    public EncryptedResourceLoader(EncryptedClassLoader encryptedClassLoader) {
        this.encryptedClassLoader = encryptedClassLoader;
        this.defaultResourceLoader = new DefaultResourceLoader();
    }

    @Override
    public Resource getResource(String location) {
        // 检查是否为加密的Mapper XML文件
        if (isEncryptedMapperXml(location)) {
            return new EncryptedResource(location, encryptedClassLoader);
        }

        // 其他资源使用默认加载器
        return defaultResourceLoader.getResource(location);
    }

    private boolean isEncryptedMapperXml(String location) {
        return location.contains("com/example/secure/dao") && location.endsWith(".xml");
    }

    @Override
    public ClassLoader getClassLoader() {
        return encryptedClassLoader;
    }
}
```

### 5. EncryptedResource实现

```java
@Slf4j
public class EncryptedResource implements Resource {

    private final String location;
    private final EncryptedClassLoader encryptedClassLoader;

    public EncryptedResource(String location, EncryptedClassLoader encryptedClassLoader) {
        this.location = location;
        this.encryptedClassLoader = encryptedClassLoader;
    }

    @Override
    public InputStream getInputStream() throws IOException {
        // 从加密目录加载XML文件
        String encryptedPath = "encrypted/" + location.replace("classpath:", "");
        byte[] encryptedData = Files.readAllBytes(Paths.get(encryptedPath));

        // 解密XML内容
        byte[] decryptedData = encryptedClassLoader.decrypt(encryptedData);

        return new ByteArrayInputStream(decryptedData);
    }

    // 实现其他Resource接口方法...
}
```

### 6. 构建配置调整

修改Maven配置，确保TestDao接口和TestMapper.xml都被加密：

```xml
<!-- 执行类文件加密 -->
<plugin>
    <groupId>org.codehaus.mojo</groupId>
    <artifactId>exec-maven-plugin</artifactId>
    <executions>
        <execution>
            <id>encrypt-classes</id>
            <phase>process-classes</phase>
            <goals>
                <goal>java</goal>
            </goals>
            <configuration>
                <mainClass>com.example.build.EncryptTool</mainClass>
                <arguments>
                    <argument>${project.build.directory}/plain</argument>
                    <argument>${project.build.directory}/encrypted</argument>
                </arguments>
            </configuration>
        </execution>

        <!-- 加密XML文件 -->
        <execution>
            <id>encrypt-xml-files</id>
            <phase>process-classes</phase>
            <goals>
                <goal>java</goal>
            </goals>
            <configuration>
                <mainClass>com.example.build.EncryptTool</mainClass>
                <arguments>
                    <argument>${project.build.outputDirectory}/mapper/TestMapper.xml</argument>
                    <argument>${project.build.directory}/encrypted/mapper/TestMapper.xml</argument>
                </arguments>
            </configuration>
        </execution>
    </executions>
</plugin>
```

### 4. JAR排除配置

```xml
<plugin>
    <groupId>org.apache.maven.plugins</groupId>
    <artifactId>maven-jar-plugin</artifactId>
    <configuration>
        <excludes>
            <exclude>com/example/secure/**</exclude>
            <!-- 新增：排除加密的Dao接口 -->
            <exclude>com/example/secure/dao/**</exclude>
        </excludes>
    </configuration>
</plugin>
```

## 风险评估

### 技术风险

1. **MyBatis版本兼容性** - MapperFactoryBean API可能在不同版本间有变化
2. **Spring容器初始化顺序** - 需要确保SqlSessionFactory在扫描器之前初始化
3. **类加载器隔离** - 需要处理不同ClassLoader间的类型转换问题

### 缓解措施

1. **版本锁定** - 明确指定MyBatis版本，避免自动升级
2. **依赖注入** - 通过@Autowired确保正确的初始化顺序
3. **类型检查** - 在注册前验证类型兼容性

## 测试策略

### 单元测试
- EncryptedMapperScanner的扫描逻辑
- MapperFactoryBean的创建和注册
- EncryptedClassLoader的Dao接口加载

### 集成测试  
- Spring容器启动和Bean注册
- MyBatis代理创建和SQL执行
- 事务管理功能验证

### 安全测试
- 验证加密文件无法直接反编译
- 验证标准ClassLoader无法加载加密接口
- 验证运行时内存中的类保护
