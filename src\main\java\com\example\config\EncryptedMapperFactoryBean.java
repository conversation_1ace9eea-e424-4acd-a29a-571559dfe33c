package com.example.config;

import com.example.security.EncryptedClassLoader;
import com.example.security.EncryptedService;

import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.builder.xml.XMLMapperBuilder;
import org.apache.ibatis.session.Configuration;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.mapper.MapperFactoryBean;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.InputStream;

/**
 * 加密Mapper工厂Bean
 * 
 * 扩展标准的MapperFactoryBean，支持加密XML文件的加载
 */
@Slf4j
public class EncryptedMapperFactoryBean<T> extends MapperFactoryBean<T> {

    private EncryptedClassLoader encryptedClassLoader;

    public void setEncryptedClassLoader(EncryptedClassLoader encryptedClassLoader) {
        this.encryptedClassLoader = encryptedClassLoader;
    }

    @Autowired
    @Override
    public void setSqlSessionFactory(SqlSessionFactory sqlSessionFactory) {
        super.setSqlSessionFactory(sqlSessionFactory);
    }

    @Override
    protected void checkDaoConfig() {
        super.checkDaoConfig();

        log.info("🔧 [ENCRYPTED-MAPPER-FACTORY] Configuring encrypted mapper: {}", getMapperInterface().getName());

        try {
            // 加载加密的XML映射文件
            loadEncryptedXmlMapping();
            log.info("✅ [ENCRYPTED-MAPPER-FACTORY] Encrypted resource loader configured for: {}", getMapperInterface().getName());
        } catch (Exception e) {
            log.error("❌ [ENCRYPTED-MAPPER-FACTORY] Failed to load encrypted XML mapping for: {}", getMapperInterface().getName(), e);
            throw new RuntimeException("Failed to load encrypted XML mapping", e);
        }
    }

    /**
     * 加载加密的XML映射文件
     */
    private void loadEncryptedXmlMapping() throws Exception {
        if (encryptedClassLoader == null) {
            log.warn("⚠️ [ENCRYPTED-MAPPER-FACTORY] EncryptedClassLoader not set, skipping XML loading");
            return;
        }

        // 获取Mapper接口名称，并转换为对应的XML文件名
        String mapperClassName = getMapperInterface().getName();
        String simpleName = mapperClassName.substring(mapperClassName.lastIndexOf('.') + 1);

        // 将Dao后缀替换为Mapper后缀（TestDao -> TestMapper）
        String xmlFileName;
        if (simpleName.endsWith("Dao")) {
            xmlFileName = simpleName.substring(0, simpleName.length() - 3) + "Mapper.xml";
        } else {
            xmlFileName = simpleName + ".xml";
        }

        log.debug("🔍 [ENCRYPTED-MAPPER-FACTORY] Looking for encrypted XML: {}", xmlFileName);

        // 检查是否存在加密的XML文件
        if (EncryptedService.getInstance().hasEncryptedXml(xmlFileName)) {
            log.info("🔐 [ENCRYPTED-MAPPER-FACTORY] Loading encrypted XML: {}", xmlFileName);

            // 加载并解密XML文件
            try (InputStream xmlStream = EncryptedService.getInstance().loadEncryptedXml(xmlFileName)) {
                if (xmlStream != null) {
                    // 获取MyBatis配置
                    Configuration configuration = getSqlSessionFactory().getConfiguration();

                    // 使用XMLMapperBuilder解析XML
                    XMLMapperBuilder xmlMapperBuilder = new XMLMapperBuilder(
                        xmlStream,
                        configuration,
                        xmlFileName,
                        configuration.getSqlFragments()
                    );

                    // 解析XML映射
                    xmlMapperBuilder.parse();

                    log.info("✅ [ENCRYPTED-MAPPER-FACTORY] Successfully loaded encrypted XML: {}", xmlFileName);
                } else {
                    log.warn("⚠️ [ENCRYPTED-MAPPER-FACTORY] Failed to decrypt XML: {}", xmlFileName);
                }
            }
        } else {
            log.debug("ℹ️ [ENCRYPTED-MAPPER-FACTORY] No encrypted XML found for: {}", xmlFileName);
        }
    }
    
    // 注意：afterPropertiesSet()是final方法，不能覆盖
    // 我们在checkDaoConfig()中进行初始化配置
}
