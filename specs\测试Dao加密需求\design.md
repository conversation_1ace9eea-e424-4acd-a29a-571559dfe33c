# 技术方案设计

## 技术架构

### 当前架构
- Spring Boot 2.6.4
- Java 8/11
- 加密类加载器机制
- AOP事务支持

### 新增组件
- MyBatis 3.x
- MySQL Connector
- 数据库连接池（HikariCP）

## 技术选型

### 数据库访问层
- **MyBatis**: 选择MyBatis作为ORM框架，因为它轻量级且与Spring Boot集成良好
- **MySQL**: 使用MySQL 8.0+作为数据库
- **HikariCP**: 作为连接池，Spring Boot默认集成

### 配置管理
- **YAML格式**: 将application.properties转换为application.yml，提供更好的可读性和层次结构

## 数据库设计

### 表结构
```sql
CREATE TABLE `test` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `contract_id` int(11) DEFAULT NULL,
  `version` int(11) DEFAULT NULL,
  `token_id` int(11) DEFAULT NULL,
  `user_address` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `deleted` int(11) DEFAULT '0',
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=51 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

## 接口设计

### TestDao接口
```java
public interface TestDao {
    // 查询方法
    TestEntity selectById(Integer id);
    List<TestEntity> selectAll();
    
    // 插入方法
    int insert(TestEntity entity);
}
```

### TestEntity实体类
```java
public class TestEntity {
    private Integer id;
    private Integer contractId;
    private Integer version;
    private Integer tokenId;
    private String userAddress;
    private String userName;
    private LocalDateTime createTime;
    private Integer deleted;
    private LocalDateTime updateTime;
}
```

## 项目结构

```
src/main/
├── java/com/example/
│   └── secure/
│       ├── dao/
│       │   └── TestDao.java
│       ├── entity/
│       │   └── TestEntity.java
│       └── TransactionalSecretService.java (修改)
└── resources/
    ├── application.yml (新建)
    └── mapper/
        └── TestMapper.xml
```

## 依赖配置

需要在pom.xml中添加：
- spring-boot-starter-jdbc
- mybatis-spring-boot-starter
- mysql-connector-java

## 配置文件结构

```yaml
server:
  port: 8080

spring:
  datasource:
    url: **************************************************************************
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:password}
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 10
      minimum-idle: 5

mybatis:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.example.secure.entity
  configuration:
    map-underscore-to-camel-case: true
```

## 加密类兼容性

由于TestDao和相关类位于secure包下，它们将被加密处理：
1. 确保MyBatis能够正确扫描和代理加密的Dao接口
2. 验证事务注解在加密类上的正常工作
3. 测试XML映射文件与加密类的绑定关系
