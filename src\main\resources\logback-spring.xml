<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- Console appender with colored output -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %highlight(%-5level) %cyan(%logger{36}) - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- Root logger -->
    <root level="INFO">
        <appender-ref ref="CONSOLE" />
    </root>

    <!-- Application specific loggers -->
    <logger name="com.example" level="DEBUG" />

    <!-- Encrypted components loggers -->
    <logger name="com.example.config.EncryptedBeanRegistrar" level="DEBUG" />
    <logger name="com.example.config.EncryptedMapperScanner" level="DEBUG" />
    <logger name="com.example.secureloader" level="DEBUG" />

    <!-- Spring Boot specific loggers -->
    <logger name="org.springframework.boot" level="INFO" />
    <logger name="org.springframework.context" level="INFO" />

    <!-- Reduce noise from Spring -->
    <logger name="org.springframework.boot.autoconfigure" level="WARN" />
    <logger name="org.springframework.boot.context" level="WARN" />

    <!-- MyBatis loggers -->
    <logger name="com.example.secure.dao" level="DEBUG" />

    <!-- Reduce Maven noise -->
    <logger name="org.apache.maven" level="WARN" />
</configuration>
