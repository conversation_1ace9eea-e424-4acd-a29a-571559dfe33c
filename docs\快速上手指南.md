# 快速上手指南

## 🚀 5分钟快速体验

### 步骤1：环境准备
```bash
# 确保已安装Java 8+和Maven
java -version
mvn -version
```

### 步骤2：运行项目
```bash
# 1. 进入项目目录
cd encrypted-guard-demo

# 2. 执行自动化部署脚本
./deploy.sh

# 或者手动执行以下步骤：
# 编译敏感逻辑类
mkdir -p out
javac -encoding UTF-8 -d out src/main/java/com/example/secure/SecretLogic.java

# 编译加密器
javac -encoding UTF-8 encrypt/Encryptor.java

# 执行加密
java encrypt.Encryptor out/com/example/secure/SecretLogic.class encrypted-classes/SecretLogic.enc

# 启动Spring Boot
mvn spring-boot:run
```

### 步骤3：测试功能
```bash
# 访问测试接口
curl http://localhost:8080/run-secure

# 预期输出：Secure logic executed
# 控制台会显示：This is sensitive logic!
```

## 📋 项目结构解析

```
encrypted-guard-demo/
├── src/main/java/com/example/
│   ├── EncryptedDemoApplication.java    # Spring Boot启动类
│   ├── controller/
│   │   └── TestController.java          # REST控制器
│   ├── service/
│   │   └── SecureLogicCaller.java       # 加密类调用服务
│   ├── secure/
│   │   └── SecretLogic.java             # 敏感逻辑类（会被加密）
│   └── secureloader/
│       └── EncryptedClassLoader.java    # 自定义类加载器
├── encrypt/
│   └── Encryptor.java                   # 加密工具类
├── encrypted-classes/
│   └── SecretLogic.enc                  # 加密后的类文件
├── out/                                 # 编译输出目录
├── pom.xml                              # Maven配置（包含ProGuard）
├── proguard-rules.pro                   # ProGuard混淆规则
└── deploy.sh                            # 自动化部署脚本
```

## 🔍 核心流程分析

### 1. 加密流程
```
SecretLogic.java → javac编译 → SecretLogic.class → Encryptor加密 → SecretLogic.enc
```

### 2. 运行时解密流程
```
HTTP请求 → TestController → SecureLogicCaller → EncryptedClassLoader
→ 读取.enc文件 → AES解密 → defineClass → 反射调用
```

## 🛠️ 关键代码理解

### 1. 加密工具 (Encryptor.java)
```java
// 核心功能：将.class文件加密为.enc文件
public static void main(String[] args) throws Exception {
    // 读取原始类文件 → AES加密 → 保存加密文件
    byte[] classBytes = Files.readAllBytes(input);
    Cipher cipher = Cipher.getInstance("AES");
    cipher.init(Cipher.ENCRYPT_MODE, new SecretKeySpec(AES_KEY.getBytes(), "AES"));
    byte[] encrypted = cipher.doFinal(classBytes);
    Files.write(output, encrypted);
}
```

### 2. 自定义类加载器 (EncryptedClassLoader.java)
```java
// 核心功能：运行时解密并加载类
@Override
protected Class<?> findClass(String name) throws ClassNotFoundException {
    // 读取加密文件 → AES解密 → 定义类
    byte[] encrypted = Files.readAllBytes(path);
    Cipher cipher = Cipher.getInstance("AES");
    cipher.init(Cipher.DECRYPT_MODE, new SecretKeySpec(key, "AES"));
    byte[] decrypted = cipher.doFinal(encrypted);
    return defineClass(name, decrypted, 0, decrypted.length);
}
```

### 3. 业务集成 (SecureLogicCaller.java)
```java
// 核心功能：在Spring Boot中使用加密类
public void runSecureLogic() {
    // 创建类加载器 → 加载类 → 反射调用
    EncryptedClassLoader loader = new EncryptedClassLoader(encryptedDir, key);
    Class<?> clazz = loader.loadClass("com.example.secure.service.SecretLogicService");
    Object instance = clazz.getDeclaredConstructor().newInstance();
    clazz.getMethod("run").invoke(instance);
}
```

## 🔧 常见问题排查

### 问题1：加密文件不存在
```bash
# 错误信息：java.nio.file.NoSuchFileException
# 解决方案：确保执行了加密步骤
mkdir -p encrypted-classes
java encrypt.Encryptor out/com/example/secure/SecretLogic.class encrypted-classes/SecretLogic.enc
```

### 问题2：密钥不匹配
```bash
# 错误信息：javax.crypto.BadPaddingException
# 解决方案：确保加密和解密使用相同密钥
# 检查Encryptor.java和EncryptedClassLoader.java中的密钥是否一致
```

### 问题3：类找不到
```bash
# 错误信息：ClassNotFoundException
# 解决方案：检查类名和文件名是否匹配
# SecretLogic.java → SecretLogic.class → SecretLogic.enc
```

### 问题4：ProGuard混淆失败
```bash
# 错误信息：ProGuard processing failed
# 解决方案：检查proguard-rules.pro配置
# 确保保留了必要的类和方法
```

## 🎯 实验任务

### 任务1：修改敏感逻辑
1. 编辑 `src/main/java/com/example/secure/SecretLogic.java`
2. 修改输出内容
3. 重新编译和加密
4. 测试新的输出

### 任务2：添加新的加密类
1. 创建新的敏感类 `SecretLogic2.java`
2. 实现加密流程
3. 修改 `SecureLogicCaller` 调用新类
4. 测试功能

### 任务3：测试ProGuard混淆
```bash
# 1. 构建混淆版本
mvn clean package

# 2. 查看混淆后的JAR
jar -tf target/springboot-encrypted-demo.jar | grep com/example

# 3. 使用反编译工具查看混淆效果
# 推荐工具：JD-GUI, Fernflower
```

### 任务4：实现远程密钥
1. 创建 `RemoteKeyProvider` 类
2. 实现HTTP密钥获取
3. 修改 `SecureLogicCaller` 使用远程密钥
4. 测试密钥获取功能

## 📚 进阶学习建议

### 1. 深入理解类加载机制
- 阅读《深入理解Java虚拟机》第7章
- 实验不同的类加载器层次结构
- 理解双亲委派模型的打破

### 2. 加密算法优化
- 学习其他对称加密算法（DES, 3DES）
- 了解非对称加密在密钥交换中的应用
- 实现密钥轮换机制

### 3. 混淆技术深入
- 学习字符串加密技术
- 了解控制流混淆
- 研究反调试和反分析技术

### 4. 性能优化
- 实现类加载缓存
- 优化加密解密性能
- 监控和分析性能瓶颈

## 🔗 相关资源

### 官方文档
- [Java ClassLoader文档](https://docs.oracle.com/javase/8/docs/api/java/lang/ClassLoader.html)
- [Java Cryptography Architecture](https://docs.oracle.com/javase/8/docs/technotes/guides/security/crypto/CryptoSpec.html)
- [ProGuard用户手册](https://www.guardsquare.com/proguard/manual)

### 工具推荐
- **反编译工具**：JD-GUI, Fernflower, CFR
- **字节码分析**：javap, ASM, Bytecode Viewer
- **混淆工具**：ProGuard, Allatori, DashO

### 安全资源
- [OWASP Java安全指南](https://owasp.org/www-project-top-ten/)
- [Java安全编程最佳实践](https://wiki.sei.cmu.edu/confluence/display/java/Java+Coding+Guidelines)

## ⚡ 下一步行动

1. **立即开始**：运行项目，体验基本功能
2. **深入理解**：阅读核心代码，理解实现原理
3. **动手实践**：完成实验任务，加深理解
4. **扩展学习**：根据学习路线深入特定技术
5. **实际应用**：在自己的项目中应用相关技术
