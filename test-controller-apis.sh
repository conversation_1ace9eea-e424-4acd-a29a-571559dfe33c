#!/bin/bash

# TestController API 测试脚本
# 用于测试所有的接口功能

BASE_URL="http://localhost:8080"

echo "=== TestController API 测试开始 ==="
echo "基础URL: $BASE_URL"
echo ""

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 测试函数
test_api() {
    local name="$1"
    local url="$2"
    local method="${3:-GET}"
    
    echo -e "${YELLOW}测试: $name${NC}"
    echo "URL: $url"
    echo "方法: $method"
    
    if [ "$method" = "GET" ]; then
        response=$(curl -s -w "\n%{http_code}" "$url")
    else
        response=$(curl -s -w "\n%{http_code}" -X "$method" "$url")
    fi
    
    http_code=$(echo "$response" | tail -n1)
    body=$(echo "$response" | head -n -1)
    
    if [ "$http_code" = "200" ]; then
        echo -e "${GREEN}✓ 成功 (HTTP $http_code)${NC}"
        echo "响应: $body"
    else
        echo -e "${RED}✗ 失败 (HTTP $http_code)${NC}"
        echo "响应: $body"
    fi
    echo "----------------------------------------"
}

# 1. 基础功能测试
echo "=== 1. 基础功能测试 ==="

test_api "旧版本兼容接口" "$BASE_URL/run-secure"

test_api "直接注入加密类" "$BASE_URL/run-secure-direct"

test_api "高级加密服务(默认参数)" "$BASE_URL/run-advanced"

test_api "高级加密服务(自定义参数)" "$BASE_URL/run-advanced?input=custom-test-data"

# 2. 权限验证测试
echo ""
echo "=== 2. 权限验证测试 ==="

test_api "权限验证(默认参数)" "$BASE_URL/validate-permission"

test_api "权限验证(自定义用户)" "$BASE_URL/validate-permission?userId=admin&operation=WRITE_DATA"

test_api "复杂业务规则验证(默认)" "$BASE_URL/validate-business-rule"

test_api "复杂业务规则验证(自定义)" "$BASE_URL/validate-business-rule?userId=testuser&data=sensitive-data&operation=DELETE_SENSITIVE"

# 3. 事务功能测试
echo ""
echo "=== 3. 事务功能测试 ==="

test_api "基础事务测试" "$BASE_URL/test-transaction"

test_api "自定义事务测试" "$BASE_URL/test-transaction?data=my-transaction-data"

test_api "事务回滚测试(成功)" "$BASE_URL/test-transaction-rollback?shouldFail=false"

test_api "事务回滚测试(失败)" "$BASE_URL/test-transaction-rollback?shouldFail=true"

test_api "嵌套事务测试" "$BASE_URL/test-nested-transaction"

test_api "只读事务测试" "$BASE_URL/test-readonly-transaction"

test_api "只读事务测试(自定义查询)" "$BASE_URL/test-readonly-transaction?query=custom-query-data"

# 4. 数据库功能测试
echo ""
echo "=== 4. 数据库功能测试 ==="

test_api "数据库查询测试" "$BASE_URL/test-database-query"

test_api "数据库插入测试(默认)" "$BASE_URL/test-database-insert"

test_api "数据库插入测试(自定义)" "$BASE_URL/test-database-insert?data=custom-db-data"

# 5. 监控和状态测试
echo ""
echo "=== 5. 监控和状态测试 ==="

test_api "获取性能统计" "$BASE_URL/performance-stats"

test_api "重置性能统计" "$BASE_URL/reset-performance-stats"

test_api "获取服务状态" "$BASE_URL/service-status"

echo ""
echo "=== TestController API 测试完成 ==="
