package com.example.security;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

import static org.junit.jupiter.api.Assertions.*;

/**
 * EncryptedService 测试类
 */
class EncryptedServiceTest {

    private EncryptedService encryptedService;

    @BeforeEach
    void setUp() {
        encryptedService = new EncryptedService();
    }

    @Test
    void testKeySource() throws Exception {
        // 首先刷新密钥以初始化keySource
        encryptedService.refreshKey();

        // 测试密钥获取
        String keySource = encryptedService.getKeySource();
        assertNotNull(keySource);
        // 根据DefaultKeyProvider的实现，应该返回"default key (development only)"
        assertEquals("default key (development only)", keySource);
    }

    @Test
    void testHasEncryptedXml() {
        // 测试不存在的XML文件
        assertFalse(encryptedService.hasEncryptedXml("nonexistent.xml"));
    }

    @Test
    void testHasEncryptedClass() {
        // 测试不存在的类文件
        assertFalse(encryptedService.hasEncryptedClass("com.example.NonExistentClass"));
    }

    @Test
    void testLoadEncryptedXml() {
        // 测试加载不存在的XML文件
        InputStream result = encryptedService.loadEncryptedXml("nonexistent.xml");
        assertNull(result);
    }

    @Test
    void testLoadEncryptedClass() {
        // 测试加载不存在的类文件
        assertThrows(ClassNotFoundException.class, () -> {
            encryptedService.loadEncryptedClass("com.example.NonExistentClass");
        });
    }

    @Test
    void testKeyRefresh() {
        // 测试密钥刷新功能
        assertTrue(encryptedService.needsKeyRefresh()); // 新创建的服务需要刷新（因为lastRefreshTime为0）

        // 测试刷新操作
        assertDoesNotThrow(() -> {
            encryptedService.refreshKey();
        });

        // 刷新后应该不需要再次刷新
        assertFalse(encryptedService.needsKeyRefresh());
    }
}
