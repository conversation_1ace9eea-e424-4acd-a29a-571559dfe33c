# Dao层加密实现方案分析

## 背景分析

当前项目已经实现了基于自定义ClassLoader的加密类加载机制，能够对`com.example.secure`包下的类进行AES加密保护。但是MyBatis的Dao接口如果被加密，会导致MyBatis无法通过反射创建代理实现，因为：

1. **MyBatis工作原理**：MyBatis通过`@MapperScan`扫描Dao接口，然后使用JDK动态代理创建接口实现
2. **加密类访问限制**：加密的类只能通过`EncryptedClassLoader`访问，而MyBatis使用标准的反射机制
3. **代理创建时机**：MyBatis在Spring容器初始化时创建代理，此时可能无法访问加密的接口

## 方案对比

### 方案1：自定义MyBatis扫描器（推荐）

**核心思路**：扩展MyBatis的扫描机制，使其能够识别和处理加密的Dao接口。

**技术实现**：
```java
@Component
public class EncryptedMapperScanner implements BeanDefinitionRegistryPostProcessor {
    
    @Override
    public void postProcessBeanDefinitionRegistry(BeanDefinitionRegistry registry) {
        // 1. 使用EncryptedClassLoader扫描加密的Dao接口
        // 2. 手动创建MapperFactoryBean
        // 3. 注册到Spring容器
    }
}
```

**优势**：
- ✅ 保持Dao接口加密
- ✅ 与现有加密机制完美集成
- ✅ 支持所有MyBatis功能（事务、缓存等）
- ✅ 对业务代码透明

**劣势**：
- ❌ 实现复杂度较高
- ❌ 需要深度定制MyBatis机制
- ❌ 可能与MyBatis版本升级冲突

**适用场景**：对Dao接口安全性要求极高的场景

---

### 方案2：加密Dao实现类（中等推荐）

**核心思路**：保持Dao接口不加密，但创建加密的Dao实现类。

**技术实现**：
```java
// 非加密接口
public interface TestDao {
    TestEntity selectById(Integer id);
}

// 加密实现类
@Component("testDao")
public class TestDaoImpl implements TestDao {
    @Autowired
    private SqlSessionTemplate sqlSessionTemplate;
    
    @Override
    public TestEntity selectById(Integer id) {
        return sqlSessionTemplate.selectOne("TestDao.selectById", id);
    }
}
```

**优势**：
- ✅ 核心实现逻辑被加密保护
- ✅ 实现相对简单
- ✅ 与Spring集成良好
- ✅ 支持复杂业务逻辑加密

**劣势**：
- ❌ 需要手写大量实现代码
- ❌ 失去MyBatis自动代理的便利性
- ❌ 接口定义仍然暴露

**适用场景**：需要在Dao层加入复杂业务逻辑的场景

---

### 方案3：运行时动态代理替换（高级方案）

**核心思路**：让MyBatis正常创建代理，然后在运行时替换为加密版本。

**技术实现**：
```java
@Component
public class EncryptedDaoProxyReplacer implements BeanPostProcessor {
    
    @Override
    public Object postProcessAfterInitialization(Object bean, String beanName) {
        if (isTargetDao(bean)) {
            // 创建加密版本的代理
            return createEncryptedProxy(bean);
        }
        return bean;
    }
}
```

**优势**：
- ✅ 对现有代码改动最小
- ✅ 保持MyBatis原生功能
- ✅ 可以选择性加密特定Dao

**劣势**：
- ❌ 实现复杂，调试困难
- ❌ 性能开销较大
- ❌ 可能与Spring AOP冲突

**适用场景**：需要对现有项目进行最小改动的场景

---

### 方案4：基于字节码增强（最高安全性）

**核心思路**：在构建期对Dao接口进行字节码增强，注入加密逻辑。

**技术实现**：
```xml
<!-- Maven插件 -->
<plugin>
    <groupId>org.apache.maven.plugins</groupId>
    <artifactId>maven-antrun-plugin</artifactId>
    <executions>
        <execution>
            <phase>process-classes</phase>
            <goals>
                <goal>run</goal>
            </goals>
            <configuration>
                <target>
                    <!-- 使用ASM或Javassist增强Dao接口 -->
                </target>
            </configuration>
        </execution>
    </executions>
</plugin>
```

**优势**：
- ✅ 最高级别的安全保护
- ✅ 运行时性能最优
- ✅ 完全透明的加密

**劣势**：
- ❌ 实现极其复杂
- ❌ 调试和维护困难
- ❌ 对构建工具链要求高

**适用场景**：军工、金融等对安全性要求极高的场景

---

### 方案5：混合加密策略（当前采用）

**核心思路**：接口不加密，实体类和Service层加密。

**当前实现**：
```
com.example.dao.TestDao          (不加密 - MyBatis可访问)
com.example.secure.entity.*     (加密 - 数据模型保护)  
com.example.secure.*Service     (加密 - 业务逻辑保护)
```

**优势**：
- ✅ 实现简单，风险可控
- ✅ 保护核心业务逻辑和数据模型
- ✅ 与MyBatis完美兼容
- ✅ 开发效率高

**劣势**：
- ❌ Dao接口定义暴露
- ❌ SQL映射文件暴露
- ❌ 数据访问模式可被分析

**适用场景**：大多数企业级应用场景

## 推荐方案选择

### 安全性要求分级

**低安全性要求**：方案5（混合加密策略）
- 适用于一般企业应用
- 开发效率高，维护成本低

**中等安全性要求**：方案2（加密Dao实现类）
- 适用于有一定安全要求的应用
- 平衡了安全性和开发效率

**高安全性要求**：方案1（自定义MyBatis扫描器）
- 适用于对数据访问层有严格保护要求的应用
- 需要投入较多开发资源

**极高安全性要求**：方案4（字节码增强）
- 适用于军工、金融等关键领域
- 需要专业的安全团队支持

## 结合当前项目的建议

基于当前项目的技术栈和实现复杂度，建议采用**方案1（自定义MyBatis扫描器）**作为主要实现方案，原因：

1. **技术基础完备**：项目已有完整的`EncryptedClassLoader`和`EncryptedBeanRegistrar`机制
2. **安全性提升明显**：能够真正保护Dao接口不被反编译
3. **扩展性良好**：可以基于现有框架进行扩展
4. **学习价值高**：深度理解MyBatis和Spring的集成机制

同时提供**方案2（加密Dao实现类）**作为备选方案，在方案1实现困难时可以快速切换。
