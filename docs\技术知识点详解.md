# Java 代码保护技术知识点详解

## 1. 自定义类加载器 (Custom ClassLoader)

### 1.1 基本概念

Java类加载器是JVM的重要组成部分，负责将.class文件加载到内存中并转换为Class对象。

**类加载器层次结构**：
```
Bootstrap ClassLoader (启动类加载器)
    ↓
Extension ClassLoader (扩展类加载器)  
    ↓
Application ClassLoader (应用类加载器)
    ↓
Custom ClassLoader (自定义类加载器)
```

### 1.2 双亲委派模型

```java
protected Class<?> loadClass(String name, boolean resolve) {
    // 1. 检查类是否已经加载
    Class<?> c = findLoadedClass(name);
    if (c == null) {
        // 2. 委派给父类加载器
        if (parent != null) {
            c = parent.loadClass(name, false);
        } else {
            c = findBootstrapClassOrNull(name);
        }
        // 3. 父类加载器无法加载时，调用自己的findClass
        if (c == null) {
            c = findClass(name);
        }
    }
    return c;
}
```

### 1.3 项目中的实现分析

<augment_code_snippet path="src/main/java/com/example/secureloader/EncryptedClassLoader.java" mode="EXCERPT">
```java
public class EncryptedClassLoader extends ClassLoader {
    private final String baseDir;
    private final byte[] key;

    @Override
    protected Class<?> findClass(String name) throws ClassNotFoundException {
        try {
            // 1. 构造加密文件路径
            String filename = name.substring(name.lastIndexOf('.') + 1) + ".enc";
            Path path = Paths.get(baseDir, filename);
            
            // 2. 读取加密文件
            byte[] encrypted = Files.readAllBytes(path);

            // 3. AES解密
            Cipher cipher = Cipher.getInstance("AES");
            cipher.init(Cipher.DECRYPT_MODE, new SecretKeySpec(key, "AES"));
            byte[] decrypted = cipher.doFinal(encrypted);

            // 4. 定义类
            return defineClass(name, decrypted, 0, decrypted.length);
        } catch (Exception e) {
            throw new ClassNotFoundException(name, e);
        }
    }
}
```
</augment_code_snippet>

**关键方法说明**：
- `findClass()`: 自定义类查找逻辑
- `defineClass()`: 将字节码转换为Class对象
- `Files.readAllBytes()`: 读取加密文件
- `Cipher.doFinal()`: 执行解密操作

## 2. AES加密技术

### 2.1 AES算法基础

**AES (Advanced Encryption Standard)**：
- 对称加密算法
- 支持128、192、256位密钥
- 块大小固定为128位

### 2.2 Java中的AES实现

<augment_code_snippet path="encrypt/Encryptor.java" mode="EXCERPT">
```java
public class Encryptor {
    private static final String AES_KEY = "1234567890abcdef"; // 16字节密钥

    public static void main(String[] args) throws Exception {
        // 1. 读取原始类文件
        byte[] classBytes = Files.readAllBytes(input);

        // 2. 初始化AES加密器
        Cipher cipher = Cipher.getInstance("AES");
        cipher.init(Cipher.ENCRYPT_MODE, new SecretKeySpec(AES_KEY.getBytes(), "AES"));
        
        // 3. 执行加密
        byte[] encrypted = cipher.doFinal(classBytes);

        // 4. 保存加密文件
        Files.write(output, encrypted);
    }
}
```
</augment_code_snippet>

**关键API说明**：
- `Cipher.getInstance("AES")`: 获取AES加密器实例
- `SecretKeySpec`: 构造对称密钥
- `cipher.init()`: 初始化加密/解密模式
- `cipher.doFinal()`: 执行加密/解密操作

### 2.3 密钥管理最佳实践

```java
// ❌ 不安全：硬编码密钥
private static final String AES_KEY = "1234567890abcdef";

// ✅ 安全：从环境变量获取
String key = System.getenv("AES_KEY");

// ✅ 更安全：从远程服务获取
public class RemoteKeyProvider {
    public static String getKey() {
        // 从远程API获取密钥
        URL url = new URL("https://yourserver.com/api/aes-key");
        // 添加认证、重试、缓存等机制
    }
}
```

## 3. ProGuard代码混淆

### 3.1 混淆原理

**代码混淆的目的**：
- 类名混淆：`UserService` → `a`
- 方法名混淆：`getUserInfo()` → `b()`
- 字段名混淆：`userName` → `c`
- 控制流混淆：增加无用的跳转和分支

### 3.2 Maven插件配置

<augment_code_snippet path="pom.xml" mode="EXCERPT">
```xml
<plugin>
    <groupId>com.github.wvengen</groupId>
    <artifactId>proguard-maven-plugin</artifactId>
    <configuration>
        <obfuscate>true</obfuscate>
        <options>
            <!-- 保留Spring Boot注解 -->
            <option>-keepattributes *Annotation*</option>
            
            <!-- 保留特定类不被混淆 -->
            <option>-keep class com.example.controller.** { *; }</option>
            <option>-keep class com.example.service.** { *; }</option>
            
            <!-- 保留Spring相关注解的方法 -->
            <option>
                -keep class * {
                @org.springframework.web.bind.annotation.GetMapping *;
                @org.springframework.stereotype.Service *;
                }
            </option>
        </options>
    </configuration>
</plugin>
```
</augment_code_snippet>

### 3.3 混淆规则详解

```proguard
# 基础配置
-dontwarn                    # 忽略警告
-dontshrink                  # 不删除未使用的代码
-dontoptimize               # 不优化代码

# 保留注解
-keepattributes *Annotation*

# 保留入口类
-keep public class com.example.EncryptedDemoApplication {
    public static void main(java.lang.String[]);
}

# 保留Spring Boot相关
-keep class org.springframework.** { *; }

# 保留反射调用的类
-keep class com.example.secure.** { *; }
```

## 4. Spring Boot集成

### 4.1 服务层实现

<augment_code_snippet path="src/main/java/com/example/service/SecureLogicCaller.java" mode="EXCERPT">
```java
@Service
public class SecureLogicCaller {
    public void runSecureLogic() {
        try {
            // 1. 获取密钥（实际项目中应从安全位置获取）
            String key = "1234567890abcdef";
            String encryptedDir = "encrypted-classes";
            
            // 2. 创建自定义类加载器
            EncryptedClassLoader loader = new EncryptedClassLoader(encryptedDir, key);
            
            // 3. 动态加载加密的类
            Class<?> clazz = loader.loadClass("com.example.secure.service.SecretLogicService");
            
            // 4. 通过反射创建实例并调用方法
            Object instance = clazz.getDeclaredConstructor().newInstance();
            clazz.getMethod("run").invoke(instance);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
```
</augment_code_snippet>

### 4.2 控制器层实现

<augment_code_snippet path="src/main/java/com/example/controller/TestController.java" mode="EXCERPT">
```java
@RestController
public class TestController {
    @Autowired
    SecureLogicCaller caller;

    @GetMapping("/run-secure")
    public String run() {
        caller.runSecureLogic();
        return "Secure logic executed";
    }
}
```
</augment_code_snippet>

## 5. 反射技术应用

### 5.1 动态类加载和方法调用

```java
// 1. 加载类
Class<?> clazz = classLoader.loadClass("com.example.secure.service.SecretLogicService");

// 2. 创建实例
Object instance = clazz.getDeclaredConstructor().newInstance();

// 3. 获取方法
Method method = clazz.getMethod("run");

// 4. 调用方法
method.invoke(instance);

// 5. 带参数的方法调用
Method methodWithParams = clazz.getMethod("process", String.class, int.class);
Object result = methodWithParams.invoke(instance, "test", 123);
```

### 5.2 异常处理

```java
try {
    // 反射操作
} catch (ClassNotFoundException e) {
    // 类未找到
} catch (NoSuchMethodException e) {
    // 方法不存在
} catch (IllegalAccessException e) {
    // 访问权限不足
} catch (InvocationTargetException e) {
    // 方法执行异常
    Throwable cause = e.getCause(); // 获取真实异常
}
```

## 6. 安全考虑

### 6.1 密钥安全

```java
// ❌ 不安全的做法
public class UnsafeKeyManager {
    private static final String KEY = "hardcoded-key"; // 硬编码
    
    public static String getKey() {
        return KEY; // 直接返回
    }
}

// ✅ 安全的做法
public class SafeKeyManager {
    private static String cachedKey;
    private static long keyExpireTime;
    
    public static String getKey() {
        if (cachedKey == null || System.currentTimeMillis() > keyExpireTime) {
            cachedKey = fetchKeyFromRemote(); // 从远程获取
            keyExpireTime = System.currentTimeMillis() + 3600000; // 1小时过期
        }
        return cachedKey;
    }
    
    private static String fetchKeyFromRemote() {
        // 实现远程密钥获取逻辑
        // 包含认证、重试、超时处理
    }
}
```

### 6.2 反编译对抗

```java
// 字符串加密
public class StringEncryption {
    private static final String ENCRYPTED_STRING = "encrypted_value";
    
    public static String decrypt(String encrypted) {
        // 实现字符串解密逻辑
    }
}

// 反调试检测
public class AntiDebug {
    public static boolean isDebugging() {
        // 检测调试器
        return ManagementFactory.getRuntimeMXBean()
            .getInputArguments().toString().contains("jdwp");
    }
}
```

## 7. 性能优化

### 7.1 类加载缓存

```java
public class CachedEncryptedClassLoader extends ClassLoader {
    private final Map<String, Class<?>> classCache = new ConcurrentHashMap<>();
    
    @Override
    protected Class<?> findClass(String name) throws ClassNotFoundException {
        // 检查缓存
        Class<?> cached = classCache.get(name);
        if (cached != null) {
            return cached;
        }
        
        // 加载并缓存
        Class<?> clazz = loadAndDecryptClass(name);
        classCache.put(name, clazz);
        return clazz;
    }
}
```

### 7.2 密钥缓存

```java
public class KeyCache {
    private static final long CACHE_DURATION = 3600000; // 1小时
    private static String cachedKey;
    private static long cacheTime;
    
    public static synchronized String getKey() {
        long now = System.currentTimeMillis();
        if (cachedKey == null || (now - cacheTime) > CACHE_DURATION) {
            cachedKey = fetchNewKey();
            cacheTime = now;
        }
        return cachedKey;
    }
}
```
