
#!/bin/bash

set -e

echo "== 编译敏感逻辑类 =="
mkdir -p out
javac -encoding UTF-8 -d out src/main/java/com/example/secure/SecretLogic.java

echo "== 编译加密器 =="
javac -encoding UTF-8 encrypt/Encryptor.java

echo "== 执行加密 =="
java encrypt.Encryptor out/com/example/secure/SecretLogic.class encrypted-classes/SecretLogic.enc

echo "== 编译 Spring Boot 项目 =="
mvn clean package -DskipTests

echo "== 启动 Spring Boot =="
java -jar target/springboot-encrypted-demo.jar
