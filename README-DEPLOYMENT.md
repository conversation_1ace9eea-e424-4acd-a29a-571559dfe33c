# Spring Boot 加密应用部署指南

## 📁 文件结构说明

### 构建后的完整文件结构

```
encrypted-guard-demo/
├── 🚀 启动相关文件
│   ├── start-encrypted-app.bat          # Windows启动脚本
│   ├── start-encrypted-app.sh           # Linux/Mac启动脚本
│   └── verify-deployment.sh             # 部署验证脚本
│
├── 📦 应用文件
│   ├── target/
│   │   ├── springboot-encrypted-demo.jar     # ⭐ 主JAR包（不含敏感类）
│   │   ├── encrypted/                        # 构建时生成的加密文件
│   │   │   └── com/example/secure/
│   │   │       ├── SecretLogic.class         # 🔒 AES加密
│   │   │       ├── AdvancedSecretService.class
│   │   │       └── TransactionalSecretService.class
│   │   └── dependency/                       # Maven依赖JAR包
│   │       ├── spring-boot-starter-web-2.6.4.jar
│   │       └── ... (其他依赖)
│   │
│   └── encrypted/                            # ⭐ 运行时加密文件目录
│       └── com/example/secure/               # 从target/encrypted复制
│           ├── SecretLogic.class             # 🔒 运行时解密
│           ├── AdvancedSecretService.class
│           └── TransactionalSecretService.class
│
└── 📚 文档和配置
    ├── specs/Spring集成加密类加载/
    │   ├── requirements.md                   # 需求文档
    │   ├── design.md                         # 技术方案
    │   ├── tasks.md                          # 任务拆分
    │   └── complete.md                       # 完成总结
    └── README-DEPLOYMENT.md                  # 本文档
```

## 🔍 文件类型详解

### 1. 主JAR包 (`target/springboot-encrypted-demo.jar`)
```bash
# 内容说明
✅ 包含: Spring Boot框架、普通业务类、启动逻辑
❌ 排除: com.example.secure包下的敏感类
🎯 用途: 标准的Spring Boot可执行JAR

# 验证命令
jar -tf target/springboot-encrypted-demo.jar | grep "com/example/secure"
# 应该没有输出（敏感类已排除）
```

### 2. 加密类文件 (`encrypted/com/example/secure/*.class`)
```bash
# 内容说明
🔒 格式: AES加密的二进制文件
❌ 无法: 直接反编译或执行
🎯 用途: 运行时由EncryptedClassLoader解密加载

# 验证命令
file encrypted/com/example/secure/SecretLogic.class
# 输出: data (不是Java class文件)

file target/classes/com/example/service/SecureLogicCaller.class 
target/classes/com/example/service/SecureLogicCaller.class: compiled Java class data, version 55.0 (Java SE 11)
```

### 3. 依赖JAR包 (`target/dependency/*.jar`)
```bash
# 内容说明
📦 包含: Spring Boot及第三方依赖
🔓 格式: 标准JAR包，未加密
🎯 用途: 提供框架和工具类支持

# 验证命令
ls target/dependency/ | wc -l
# 输出: 依赖JAR包数量
```

## 🚀 快速启动

### Windows环境
```cmd
# 1. 验证部署
verify-deployment.sh  # 如果有Git Bash

# 2. 启动应用
start-encrypted-app.bat

# 3. 手动启动
java -jar target\springboot-encrypted-demo.jar
```

### Linux/Mac环境
```bash
# 1. 验证部署
./verify-deployment.sh

# 2. 启动应用
./start-encrypted-app.sh

# 3. 手动启动
java -jar target/springboot-encrypted-demo.jar
```

## 🔧 启动模式详解

### 1. 生产模式（默认）
```bash
java -jar target/springboot-encrypted-demo.jar

# 特点:
# ✅ 使用加密类文件
# ✅ 使用默认密钥或环境变量密钥
# ✅ 完整的代码保护
```

### 2. 开发模式
```bash
java -Ddev.mode=true -jar target/springboot-encrypted-demo.jar

# 特点:
# 🔓 跳过加密，直接使用JAR中的类
# 🚀 启动更快，便于开发调试
# ⚠️ 无代码保护
```

### 3. 调试模式
```bash
java -Dencrypt.debug=true -jar target/springboot-encrypted-demo.jar

# 特点:
# 🔍 详细的类加载日志
# 📊 性能统计信息
# 🔒 仍使用加密保护
# 💾 显示缓存命中情况
```

### 4. 自定义密钥模式
```bash
# 方式1: 系统属性
java -Ddecrypt.key=your-secret-key -jar target/springboot-encrypted-demo.jar

# 方式2: 环境变量
export DECRYPT_KEY=your-secret-key
java -jar target/springboot-encrypted-demo.jar

# 方式3: 启动时输入（使用启动脚本）
./start-encrypted-app.sh  # 选择模式4
```

### 5. 性能监控模式
```bash
# 启用性能监控和详细统计
java -Dencrypt.debug=true -Dperformance.monitor=true -jar target/springboot-encrypted-demo.jar

# 特点:
# 📈 详细的性能指标
# ⏱️ 类加载时间统计
# 💾 缓存命中率监控
# 🚨 性能警告提示
```

### 6. 自定义加密目录模式
```bash
# 指定自定义的加密文件目录
java -Dencrypted.dir=/custom/path/encrypted -jar target/springboot-encrypted-demo.jar

# 用于分离部署场景
```

## 🔍 部署验证

### 自动验证
```bash
# Linux/Mac
./verify-deployment.sh

# 验证内容:
# ✅ 文件结构完整性
# ✅ 加密文件存在性
# ✅ JAR包内容正确性
# ✅ 依赖完整性
```

### 手动验证
```bash
# 1. 检查主JAR包
ls -la target/springboot-encrypted-demo.jar

# 2. 检查加密文件
ls -la encrypted/com/example/secure/
file encrypted/com/example/secure/*.class

# 3. 检查JAR包内容
jar -tf target/springboot-encrypted-demo.jar | grep "com/example/secure"
# 应该没有输出

# 4. 验证应用功能
curl http://localhost:8080/service-status
curl http://localhost:8080/run-secure-direct
```

## 🌐 功能验证接口

### 基础功能测试
```bash
# 服务状态检查 - 验证所有加密服务是否正常注入
curl http://localhost:8080/service-status

# 直接调用加密服务 - 测试SecretLogic核心功能
curl http://localhost:8080/run-secure-direct

# 高级加密服务 - 测试AdvancedSecretService
curl "http://localhost:8080/run-advanced?input=test-data"

# 兼容性接口 - 旧版本手动加载方式
curl http://localhost:8080/run-secure
```

### Spring特性测试
```bash
# 权限验证 - 测试加密类中的权限逻辑
curl "http://localhost:8080/validate-permission?userId=admin&operation=READ_DATA"

# 业务规则验证 - 测试复杂业务逻辑
curl "http://localhost:8080/validate-business-rule?userId=admin&data=test&operation=READ"

# 敏感数据处理 - 测试数据处理功能
curl "http://localhost:8080/process-sensitive-data?data=confidential-info"
```

### 事务功能测试
```bash
# 基础事务测试 - 验证@Transactional注解
curl http://localhost:8080/test-transaction

# 事务回滚测试 - 验证异常回滚机制
curl "http://localhost:8080/test-transaction-rollback?shouldFail=true"

# 嵌套事务测试 - 验证事务传播行为
curl http://localhost:8080/test-nested-transaction

# 只读事务测试 - 验证只读事务功能
curl "http://localhost:8080/test-readonly-transaction?query=test-query"
```

### 性能监控与统计
```bash
# 性能统计查看 - 获取详细的性能报告
curl http://localhost:8080/performance-stats

# 重置性能统计 - 清空统计数据
curl http://localhost:8080/reset-performance-stats

# 缓存统计 - 查看类加载缓存情况
curl http://localhost:8080/cache-stats
```

### AOP功能验证
```bash
# 执行任意加密服务方法，观察控制台AOP日志输出：
# 🔒 [AOP-BEFORE] Executing secure method: SecretLogic.performSecretCalculation()
# ⏱️ [AOP-AROUND] Starting secure method: SecretLogic.performSecretCalculation()
# ✅ [AOP-AROUND] Secure method completed in 15ms: SecretLogic.performSecretCalculation()
# 📤 [AOP-RETURN] Secure method returned: SecretLogic.performSecretCalculation() -> Secret calculation result: 3367
# 🔓 [AOP-AFTER] Completed secure method: SecretLogic.performSecretCalculation()
```

## 🐳 Docker部署

### Dockerfile示例
```dockerfile
FROM openjdk:11-jre-slim

# 创建应用目录
WORKDIR /app

# 复制主JAR包
COPY target/springboot-encrypted-demo.jar app.jar

# 复制加密文件
COPY encrypted/ encrypted/

# 暴露端口
EXPOSE 8080

# 启动应用
CMD ["java", "-jar", "app.jar"]
```

### 构建和运行
```bash
# 构建镜像
docker build -t encrypted-spring-app .

# 运行容器
docker run -p 8080:8080 encrypted-spring-app

# 使用自定义密钥运行
docker run -p 8080:8080 -e DECRYPT_KEY=your-key encrypted-spring-app
```

## ⚠️ 常见问题

### 1. 应用启动失败
```bash
# 问题: ClassNotFoundException for encrypted classes
# 原因: 加密文件目录不存在或路径错误
# 解决: 确保encrypted/目录存在且包含加密文件

cp -r target/encrypted encrypted/

# 验证加密文件
ls -la encrypted/com/example/secure/
file encrypted/com/example/secure/*.class  # 应显示为data，不是Java class
```

### 2. 密钥错误
```bash
# 问题: BadPaddingException或解密失败
# 原因: 解密密钥与加密密钥不匹配
# 解决: 确保使用相同的密钥

# 查看构建时使用的密钥
echo $ENCRYPT_KEY

# 设置运行时密钥
export DECRYPT_KEY=$ENCRYPT_KEY

# 验证密钥长度（应为16、24或32字节）
echo -n "your-key" | wc -c
```

### 3. Spring Bean注入失败
```bash
# 问题: NoSuchBeanDefinitionException
# 原因: 加密类未正确注册到Spring容器
# 解决: 检查EncryptedBeanRegistrar是否正常工作

# 查看服务状态
curl http://localhost:8080/service-status

# 检查启动日志中的Bean注册信息
# 应看到: [REGISTRAR] Successfully registered: secretLogic -> com.example.secure.service.SecretLogicService
```

### 4. 性能问题
```bash
# 问题: 启动缓慢或类加载耗时
# 原因: 首次解密加载耗时
# 解决: 使用开发模式或查看性能统计

# 开发模式（跳过加密）
java -Ddev.mode=true -jar target/springboot-encrypted-demo.jar

# 查看详细性能统计
curl http://localhost:8080/performance-stats

# 启用调试模式查看加载时间
java -Dencrypt.debug=true -jar target/springboot-encrypted-demo.jar
```

### 5. AOP功能异常
```bash
# 问题: AOP切面不生效
# 原因: 代理类型不匹配
# 解决: 确保使用接口注入

# 正确方式：通过接口注入
@Autowired
private ISecretLogic secretLogic;

# 错误方式：直接注入实现类（可能导致代理问题）
@Autowired
private SecretLogic secretLogic;
```

### 6. 文件权限问题
```bash
# Linux/Mac环境
chmod +x start-encrypted-app.sh
chmod +x verify-deployment.sh

# 确保加密文件可读
chmod -R 644 encrypted/

# Windows环境
# 确保有足够的文件系统权限访问encrypted目录
```

## 📋 生产环境清单

### 部署前检查
- [ ] 主JAR包存在且大小正常 (`target/springboot-encrypted-demo.jar`)
- [ ] 加密文件目录完整 (`encrypted/com/example/secure/`)
- [ ] 三个加密类文件存在：SecretLogic.class、AdvancedSecretService.class、TransactionalSecretService.class
- [ ] 密钥配置正确（环境变量或系统属性）
- [ ] 网络端口开放（8080）
- [ ] Java版本兼容（JDK 11+）
- [ ] 依赖JAR包完整 (`target/dependency/`)

### 安全检查
- [ ] 密钥不在代码中硬编码
- [ ] 加密文件无法直接反编译 (`file encrypted/com/example/secure/*.class` 显示为data)
- [ ] JAR包中不包含敏感类 (`jar -tf target/springboot-encrypted-demo.jar | grep com/example/secure` 无输出)
- [ ] 日志中不输出敏感信息（密钥、敏感数据等）
- [ ] 加密文件与主JAR包分离存储
- [ ] 生产环境使用强密钥（非默认密钥）

### 功能检查
- [ ] 所有加密服务正常注入 (`curl http://localhost:8080/service-status`)
- [ ] 基础功能正常 (`curl http://localhost:8080/run-secure-direct`)
- [ ] Spring特性正常（依赖注入、AOP、事务）
- [ ] 性能监控正常 (`curl http://localhost:8080/performance-stats`)
- [ ] 错误处理机制正常

### 性能检查
- [ ] 启动时间在可接受范围内（< 30秒）
- [ ] 内存使用正常（基线 + 50MB以内）
- [ ] 加密类首次加载时间 < 100ms
- [ ] 缓存命中率 > 90%（运行一段时间后）
- [ ] 无性能警告日志
- [ ] 并发访问性能正常

---

🎉 **恭喜！** 您已经成功部署了一个具有企业级代码保护功能的Spring Boot应用！
