package com.example.aop;

import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.*;
import org.springframework.stereotype.Component;

/**
 * AOP切面 - 测试对加密类的AOP支持
 *
 * 验证加密类是否可以正常应用AOP切面
 *
 * 暂时禁用以简化测试
 */
//@Aspect
//@Component
public class SecretLogicAspect {
    
    /**
     * 切点：匹配所有secure包下的方法
     */
    @Pointcut("execution(* com.example.secure..*(..))")
    public void secureMethodPointcut() {}
    
    /**
     * 前置通知
     */
    @Before("secureMethodPointcut()")
    public void beforeSecureMethod(JoinPoint joinPoint) {
        String className = joinPoint.getTarget().getClass().getSimpleName();
        String methodName = joinPoint.getSignature().getName();
        
        System.out.println("🔒 [AOP-BEFORE] Executing secure method: " + 
                          className + "." + methodName + "()");
    }
    
    /**
     * 后置通知
     */
    @After("secureMethodPointcut()")
    public void afterSecureMethod(JoinPoint joinPoint) {
        String className = joinPoint.getTarget().getClass().getSimpleName();
        String methodName = joinPoint.getSignature().getName();
        
        System.out.println("🔓 [AOP-AFTER] Completed secure method: " + 
                          className + "." + methodName + "()");
    }
    
    /**
     * 环绕通知 - 记录执行时间
     */
    @Around("secureMethodPointcut()")
    public Object aroundSecureMethod(ProceedingJoinPoint joinPoint) throws Throwable {
        String className = joinPoint.getTarget().getClass().getSimpleName();
        String methodName = joinPoint.getSignature().getName();
        
        long startTime = System.currentTimeMillis();
        
        try {
            System.out.println("⏱️ [AOP-AROUND] Starting secure method: " + 
                              className + "." + methodName + "()");
            
            Object result = joinPoint.proceed();
            
            long endTime = System.currentTimeMillis();
            System.out.println("✅ [AOP-AROUND] Secure method completed in " + 
                              (endTime - startTime) + "ms: " + 
                              className + "." + methodName + "()");
            
            return result;
            
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            System.out.println("❌ [AOP-AROUND] Secure method failed after " + 
                              (endTime - startTime) + "ms: " + 
                              className + "." + methodName + "() - " + e.getMessage());
            throw e;
        }
    }
    
    /**
     * 返回通知
     */
    @AfterReturning(pointcut = "secureMethodPointcut()", returning = "result")
    public void afterReturningSecureMethod(JoinPoint joinPoint, Object result) {
        String className = joinPoint.getTarget().getClass().getSimpleName();
        String methodName = joinPoint.getSignature().getName();
        
        String resultStr = result != null ? result.toString() : "null";
        if (resultStr.length() > 100) {
            resultStr = resultStr.substring(0, 100) + "...";
        }
        
        System.out.println("📤 [AOP-RETURN] Secure method returned: " + 
                          className + "." + methodName + "() -> " + resultStr);
    }
    
    /**
     * 异常通知
     */
    @AfterThrowing(pointcut = "secureMethodPointcut()", throwing = "exception")
    public void afterThrowingSecureMethod(JoinPoint joinPoint, Throwable exception) {
        String className = joinPoint.getTarget().getClass().getSimpleName();
        String methodName = joinPoint.getSignature().getName();
        
        System.out.println("💥 [AOP-EXCEPTION] Secure method threw exception: " + 
                          className + "." + methodName + "() - " + 
                          exception.getClass().getSimpleName() + ": " + exception.getMessage());
    }
}
