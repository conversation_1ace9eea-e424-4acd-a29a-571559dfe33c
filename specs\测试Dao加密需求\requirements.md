# 需求文档

## 介绍

为encrypted-guard-demo项目添加MyBatis数据库访问层支持，用于测试加密类在数据库操作时的行为。需要配置MySQL数据库连接，创建Dao层和对应的XML映射文件，并在TransactionalSecretService中集成数据库操作。

## 需求

### 需求 1 - 配置文件格式转换和数据库配置

**用户故事：** 作为开发者，我需要将application.properties转换为YAML格式并添加MySQL数据库连接配置，以便项目能够连接到crypto_nijigen数据库。

#### 验收标准

1. When 项目启动时，系统应当使用application.yml配置文件而不是application.properties
2. When 应用连接数据库时，系统应当能够成功连接到MySQL数据库crypto_nijigen
3. When 配置加载时，系统应当正确读取数据库连接参数（URL、用户名、密码等）

### 需求 2 - 创建测试Dao层

**用户故事：** 作为开发者，我需要在secure包下创建TestDao类和对应的XML映射文件，以便测试MyBatis在加密环境下的数据库操作。

#### 验收标准

1. When 创建Dao类时，系统应当在com.example.secure包下生成TestDao接口
2. When 创建XML映射文件时，系统应当在resources/mapper目录下生成TestMapper.xml文件
3. When 定义数据库操作时，系统应当提供select和insert方法操作test表
4. When 执行数据库操作时，系统应当能够正确映射test表的所有字段（id, contract_id, version, token_id, user_address, user_name, create_time, deleted, update_time）

### 需求 3 - 集成Dao到TransactionalSecretService

**用户故事：** 作为开发者，我需要在TransactionalSecretService中使用TestDao进行数据库操作，以便验证加密类在事务环境下的数据库访问能力。

#### 验收标准

1. When TransactionalSecretService注入TestDao时，系统应当能够成功完成依赖注入
2. When 执行事务性数据库操作时，系统应当能够在事务上下文中正确执行insert和select操作
3. When 事务回滚时，系统应当能够正确回滚数据库操作
4. When 加密类执行数据库操作时，系统应当保持正常的MyBatis功能
