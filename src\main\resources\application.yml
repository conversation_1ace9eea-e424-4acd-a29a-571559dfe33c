server:
  port: 8080

spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    username: admin
    password: Mangosteen0!
    hikari:
      maximum-pool-size: 4
      minimum-idle: 2

mybatis:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.example.secure.entity
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

logging:
  level:
    com.example.secure.dao: debug
