# 代码加密与混淆技术迁移指南

## 📋 概述

本文档详细说明如何将 `encrypted-guard-demo` 项目中的代码加密和混淆技术迁移到其他Java项目中。该方案包含两个核心技术：
1. **AES字节码加密** - 对敏感类进行运行时加密保护
2. **ProGuard代码混淆** - 对整体代码进行混淆处理

## 🎯 技术架构

### 核心组件
- **EncryptTool** - 构建期加密工具
- **EncryptedClassLoader** - 运行期解密类加载器
- **ProGuard插件** - 代码混淆处理
- **Maven构建集成** - 自动化构建流程

### 工作流程
```
源码 → 编译 → 加密敏感类 → ProGuard混淆 → 打包部署
```

## 🚀 迁移步骤

### 第一步：复制核心文件

#### 1.1 加密工具类
复制以下文件到目标项目：
```
src/main/java/com/example/build/EncryptTool.java
```

#### 1.2 安全组件
复制以下文件到目标项目：
```
src/main/java/com/example/security/
├── EncryptedClassLoader.java
├── EncryptedService.java  
├── KeyProvider.java
├── DefaultKeyProvider.java
└── KeyProviderException.java
```

#### 1.3 配置文件
复制以下配置文件：
```
proguard-rules.pro
```

### 第二步：Maven配置集成

#### 2.1 添加依赖
在目标项目的 `pom.xml` 中添加必要依赖：

```xml
<dependencies>
    <!-- Spring Boot基础依赖 -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-web</artifactId>
    </dependency>
    
    <!-- Lombok (可选) -->
    <dependency>
        <groupId>org.projectlombok</groupId>
        <artifactId>lombok</artifactId>
        <optional>true</optional>
    </dependency>
</dependencies>
```

#### 2.2 构建插件配置
在 `<build><plugins>` 中添加以下插件：

```xml
<!-- 1. 复制敏感类到临时目录 -->
<plugin>
    <groupId>org.apache.maven.plugins</groupId>
    <artifactId>maven-antrun-plugin</artifactId>
    <version>3.1.0</version>
    <executions>
        <execution>
            <id>copy-secure-classes</id>
            <phase>process-classes</phase>
            <goals>
                <goal>run</goal>
            </goals>
            <configuration>
                <target>
                    <mkdir dir="${project.build.directory}/plain"/>
                    <copy todir="${project.build.directory}/plain">
                        <fileset dir="${project.build.outputDirectory}">
                            <!-- 修改为你的敏感包路径 -->
                            <include name="**/secure/**/*.class"/>
                        </fileset>
                    </copy>
                </target>
            </configuration>
        </execution>
    </executions>
</plugin>

<!-- 2. 执行类文件加密 -->
<plugin>
    <groupId>org.codehaus.mojo</groupId>
    <artifactId>exec-maven-plugin</artifactId>
    <version>3.1.0</version>
    <executions>
        <execution>
            <id>encrypt-classes</id>
            <phase>process-classes</phase>
            <goals>
                <goal>java</goal>
            </goals>
            <configuration>
                <mainClass>com.example.build.EncryptTool</mainClass>
                <arguments>
                    <argument>${project.build.directory}/plain</argument>
                    <argument>${project.build.directory}/encrypted</argument>
                </arguments>
                <systemProperties>
                    <systemProperty>
                        <key>encrypt.key</key>
                        <value>${encrypt.key}</value>
                    </systemProperty>
                </systemProperties>
            </configuration>
        </execution>
    </executions>
</plugin>

<!-- 3. 排除原始敏感类 -->
<plugin>
    <groupId>org.apache.maven.plugins</groupId>
    <artifactId>maven-jar-plugin</artifactId>
    <version>3.3.0</version>
    <configuration>
        <excludes>
            <!-- 修改为你的敏感包路径 -->
            <exclude>**/secure/**/*.class</exclude>
        </excludes>
    </configuration>
</plugin>

<!-- 4. ProGuard代码混淆 -->
<plugin>
    <groupId>com.github.wvengen</groupId>
    <artifactId>proguard-maven-plugin</artifactId>
    <version>2.7.0</version>
    <executions>
        <execution>
            <id>proguard</id>
            <phase>package</phase>
            <goals>
                <goal>proguard</goal>
            </goals>
        </execution>
    </executions>
    <configuration>
        <obfuscate>true</obfuscate>
        <injar>${project.build.finalName}.jar</injar>
        <outjar>${project.build.finalName}.jar</outjar>
        <outputDirectory>${project.build.directory}</outputDirectory>
        <options>
            <option>-dontwarn</option>
            <option>-dontshrink</option>
            <option>-dontoptimize</option>
            <option>-keepattributes Exceptions,InnerClasses,Signature,Deprecated,SourceFile,LineNumberTable,*Annotation*,EnclosingMethod</option>
            
            <!-- 保持主类 -->
            <option>-keep public class ${main.class} {
                public static void main(java.lang.String[]);
            }</option>
            
            <!-- 保持Spring框架 -->
            <option>-keep class org.springframework.** { *; }</option>
            
            <!-- 保持你的Controller和Service -->
            <option>-keep class ${your.package}.controller.** { *; }</option>
            <option>-keep class ${your.package}.service.** { *; }</option>
            
            <!-- 保持加密相关类不被混淆 -->
            <option>-keep class ${your.package}.security.** { *; }</option>
            <option>-keep class ${your.package}.build.** { *; }</option>
        </options>
        <libs>
            <lib>${java.home}/lib/jrt-fs.jar</lib>
        </libs>
    </configuration>
</plugin>
```

### 第三步：代码适配

#### 3.1 修改包路径
将复制的文件中的包路径修改为你的项目包路径：
- `com.example` → `你的项目包名`
- `com.example.secure` → `你的敏感包路径`

#### 3.2 集成EncryptedClassLoader
在你的Spring Boot主类中集成加密类加载器：

```java
@SpringBootApplication
public class YourApplication {
    
    public static void main(String[] args) {
        // 设置自定义类加载器
        Thread.currentThread().setContextClassLoader(
            new EncryptedClassLoader(Thread.currentThread().getContextClassLoader())
        );
        
        SpringApplication.run(YourApplication.class, args);
    }
}
```

#### 3.3 创建敏感业务类
将需要加密的业务逻辑放在指定的secure包下：

```java
package your.package.secure;

@Service
public class SensitiveService {
    
    public String processSecretLogic() {
        // 你的敏感业务逻辑
        return "Protected business logic result";
    }
}
```

### 第四步：构建脚本

#### 4.1 Windows构建脚本 (build.bat)
```batch
@echo off
echo ========================================
echo Building Encrypted Application
echo ========================================

echo Cleaning old files...
if exist "target" rd /s /q "target"
if exist "encrypted" rd /s /q "encrypted"

echo Building with Maven...
call mvn clean package -Dencrypt.key=your-secret-key -DskipTests
if errorlevel 1 (
    echo Error: Maven build failed
    pause
    exit /b 1
)

echo Copying encrypted files...
if exist "encrypted" rd /s /q "encrypted"
xcopy "target\encrypted" "encrypted\" /E /I /Y

echo Build completed successfully!
pause
```

#### 4.2 Linux/Mac构建脚本 (build.sh)
```bash
#!/bin/bash
set -e

echo "========================================"
echo "Building Encrypted Application"
echo "========================================"

echo "Cleaning old files..."
rm -rf target encrypted

echo "Building with Maven..."
mvn clean package -Dencrypt.key=your-secret-key -DskipTests

echo "Copying encrypted files..."
cp -r target/encrypted ./encrypted

echo "Build completed successfully!"
```

### 第五步：部署配置

#### 5.1 部署文件结构
```
production-server/
├── your-application.jar          # 主JAR包(已混淆，不含敏感类)
├── encrypted/                    # 加密的敏感类文件
│   └── your/package/secure/
├── start.sh                     # 启动脚本
└── config/                      # 配置文件
```

#### 5.2 启动脚本
```bash
#!/bin/bash
# 生产环境启动脚本

# 设置解密密钥(从环境变量或配置文件获取)
export DECRYPT_KEY="production-secret-key"

# 启动应用
java -Ddecrypt.key=$DECRYPT_KEY \
     -jar your-application.jar
```

## 🔧 自定义配置

### 修改敏感包路径
在以下文件中修改包路径常量：
1. `EncryptedClassLoader.java` - 修改 `ENCRYPTED_PACKAGE_PREFIX`
2. `pom.xml` - 修改构建插件中的包路径过滤规则
3. `proguard-rules.pro` - 修改保护规则

### 修改加密算法
如需使用其他加密算法，修改 `EncryptTool.java` 中的：
- `ALGORITHM` 常量
- 密钥长度验证逻辑
- 加密/解密实现

### 自定义密钥管理
实现 `KeyProvider` 接口来自定义密钥获取方式：
```java
@Component
public class CustomKeyProvider implements KeyProvider {
    @Override
    public String getDecryptionKey() {
        // 从远程服务、数据库或其他安全存储获取密钥
        return remoteKeyService.getKey();
    }
}
```

## ⚠️ 注意事项

1. **密钥安全**：生产环境必须使用强密钥，避免硬编码
2. **包路径**：确保敏感类的包路径配置正确
3. **依赖冲突**：注意ProGuard可能与某些框架产生冲突
4. **性能影响**：加密解密会带来一定的性能开销
5. **调试困难**：混淆后的代码调试较为困难

## 🧪 验证测试

构建完成后，验证以下几点：
1. JAR包中不包含敏感类的明文字节码
2. encrypted目录包含加密后的类文件
3. 应用能正常启动并调用敏感业务逻辑
4. 混淆后的代码反编译查看效果

## 📚 相关文档

- [ProGuard官方文档](https://www.guardsquare.com/proguard)
- [Maven Exec Plugin](https://www.mojohaus.org/exec-maven-plugin/)
- [Spring Boot ClassLoader](https://docs.spring.io/spring-boot/docs/current/reference/html/executable-jar.html)
