## 🧪 编译加密

### ✅ 编译加密逻辑类

1. 编译 `SecretLogic.java` 生成 `SecretLogic.class`

```bash
javac -encoding UTF-8 -d out src/main/java/com/example/secure/SecretLogic.java
```

2. 使用 `Encryptor` 加密:

```bash
javac -encoding UTF-8 encrypt/Encryptor.java
java encrypt.Encryptor out/com/example/secure/SecretLogic.class encrypted-classes/SecretLogic.enc
```

### ✅ 启动 Spring Boot 项目

```bash
mvn spring-boot:run
```

访问: [http://localhost:8080/run-secure](http://localhost:8080/run-secure)

输出: `This is sensitive logic!`

---

## 🔐 一、代码混淆（使用 ProGuard）

### ✅ 步骤

1. 在 `pom.xml` 中添加 ProGuard 插件（适用于 Maven 项目）:

```xml
<plugin>
  <groupId>com.github.wvengen</groupId>
  <artifactId>proguard-maven-plugin</artifactId>
  <version>2.0.17</version>
  <executions>
    <execution>
      <phase>package</phase>
      <goals><goal>proguard</goal></goals>
    </execution>
  </executions>
  <configuration>
    <obfuscate>true</obfuscate>
    <shrink>true</shrink>
    <optimize>true</optimize>
    <proguardInclude>${project.basedir}/proguard-rules.pro</proguardInclude>
  </configuration>
</plugin>
```

2. 新建 `proguard-rules.pro` 文件：

```proguard
# 保留入口类
-keep public class com.example.EncryptedDemoApplication { public static void main(java.lang.String[]); }

# 保留 Spring Boot 相关注解
-keepattributes *Annotation*
-keep class org.springframework.** { *; }
-keep class com.example.controller.** { *; }
-keep class com.example.service.** { *; }

# 其他所有内容混淆
```

3. 构建:

```bash
mvn package
```

输出的 JAR 将被混淆，逻辑类、变量名都将不可读。

---

## 🔑 二、远程密钥管理（安全强化）

### ✅ 建议做法

将 AES 密钥放在远程服务，不保存在本地 JAR 中（避免被反编译获取）：

#### 示例:

```java
public class RemoteKeyProvider {
    public static String getKey() {
        try {
            URL url = new URL("https://yourserver.com/api/aes-key");
            BufferedReader in = new BufferedReader(new InputStreamReader(url.openStream()));
            return in.readLine(); // 示例：返回 1234567890abcdef
        } catch (Exception e) {
            throw new RuntimeException("获取密钥失败", e);
        }
    }
}
```

#### 替换原来的固定密钥:

```java
String key = RemoteKeyProvider.getKey();
```

✅ 在平台动态转换密钥，增加运营方反编译和长期破解难度。

---