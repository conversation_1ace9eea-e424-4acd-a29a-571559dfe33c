package com.example.secure.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;

/**
 * Test表实体类
 * 用于测试加密环境下的MyBatis数据库操作
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TestEntity {
    
    /**
     * 主键ID
     */
    private Integer id;
    
    /**
     * 合约ID
     */
    private Integer contractId;
    
    /**
     * 版本号
     */
    private Integer version;
    
    /**
     * Token ID
     */
    private Integer tokenId;
    
    /**
     * 用户地址
     */
    private String userAddress;
    
    /**
     * 用户名
     */
    private String userName;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 删除标记 (0-未删除, 1-已删除)
     */
    private Integer deleted;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
