@echo off
echo ========================================
echo Spring Boot Encrypted Application Startup Script
echo ========================================

REM 执行打包和加密文件准备
echo 0. Preparing application...
REM 清理旧的jar包和加密文件
echo Cleaning old files...
if exist "target" rd /s /q "target"
if exist "encrypted" rd /s /q "encrypted"
REM 执行Maven打包
echo Building project with Maven...
call mvn clean package -Dencrypt.key=1234567890abcdef -DskipTests
if errorlevel 1 (
    echo Error: Maven build failed
    pause
    exit /b 1
)

REM 复制加密文件
echo Copying encrypted files...
if exist "encrypted" rd /s /q "encrypted"
xcopy "target\encrypted" "encrypted\" /E /I /Y
if errorlevel 1 (
    echo Error: Failed to copy encrypted files
    pause
    exit /b 1
)

REM 检查必要文件是否存在
echo 1. Checking file integrity...

if not exist "target\springboot-encrypted-demo.jar" (
    echo Error: Main JAR file not found - target\springboot-encrypted-demo.jar
    echo Please run: mvn clean package
    pause
    exit /b 1
)
echo Main JAR file exists: target\springboot-encrypted-demo.jar

if not exist "encrypted\com\example\secure" (
    echo Error: Encrypted files directory not found - encrypted\com\example\secure
    echo Please run: xcopy target\encrypted encrypted\ /E /I /Y
    pause
    exit /b 1
)
echo Encrypted files directory exists: encrypted\com\example\secure

REM 统计加密文件数量
set encrypted_count=0
if exist "encrypted\com\example\secure\service" (
    for /f %%i in ('dir /b encrypted\com\example\secure\service\*.class 2^>nul ^| find /c /v ""') do set /a encrypted_count+=%%i
)
if exist "encrypted\com\example\secure\dao" (
    for /f %%i in ('dir /b encrypted\com\example\secure\dao\*.class 2^>nul ^| find /c /v ""') do set /a encrypted_count+=%%i
)
if exist "encrypted\com\example\secure\entity" (
    for /f %%i in ('dir /b encrypted\com\example\secure\entity\*.class 2^>nul ^| find /c /v ""') do set /a encrypted_count+=%%i
)
set xml_count=0
if exist "encrypted\mapper" (
    for /f %%i in ('dir /b encrypted\mapper\*.xml 2^>nul ^| find /c /v ""') do set xml_count=%%i
)
set /a total_count=%encrypted_count%+%xml_count%
echo Found %encrypted_count% encrypted class files and %xml_count% encrypted XML files, total %total_count% encrypted files

echo.
echo 2. File structure overview:
echo ├── target\springboot-encrypted-demo.jar     (Main application JAR - without sensitive classes)
echo ├── encrypted\com\example\secure\            (Encrypted sensitive class files)
echo │   ├── service\                             (Service layer)
echo │   │   ├── SecretLogicService.class         (AES encrypted)
echo │   │   ├── AdvancedSecretService.class      (AES encrypted)
echo │   │   └── TransactionalSecretService.class (AES encrypted)
echo │   ├── dao\                                 (Dao layer)
echo │   │   └── TestDao.class                    (AES encrypted)
echo │   └── entity\                              (Entity layer)
echo │       └── TestEntity.class                 (AES encrypted)
echo ├── encrypted\mapper\                        (Encrypted XML configuration files)
echo │   └── TestMapper.xml                       (AES encrypted)
echo └── target\dependency\                       (Spring Boot dependency JARs)

echo.
echo 3. Startup mode selection:
echo [1] Production mode (using default key)
echo [2] Debug mode (verbose logging)
echo [3] Custom key mode
set /p mode="Please select startup mode (1-3): "

echo.
echo 4. Starting application...

if "%mode%"=="1" (
    echo Starting mode: Production mode ^(using default key^)
    echo Command: java -Ddecrypt.key=1234567890abcdef -jar target\springboot-encrypted-demo.jar
    java -Ddecrypt.key=1234567890abcdef -jar target\springboot-encrypted-demo.jar
) else if "%mode%"=="2" (
    echo Starting mode: Debug mode ^(verbose logging^)
    echo Command: java -Ddecrypt.key=1234567890abcdef -Dlogging.level.com.example=DEBUG -jar target\springboot-encrypted-demo.jar
    java -Ddecrypt.key=1234567890abcdef -Dlogging.level.com.example=DEBUG -jar target\springboot-encrypted-demo.jar
) else if "%mode%"=="3" (
    set /p custom_key="Please enter decryption key: "
    echo Starting mode: Custom key mode
    echo Command: java -Ddecrypt.key=%custom_key% -jar target\springboot-encrypted-demo.jar
    java -Ddecrypt.key=%custom_key% -jar target\springboot-encrypted-demo.jar
) else (
    echo Invalid selection, using default production mode
    java -Ddecrypt.key=1234567890abcdef -jar target\springboot-encrypted-demo.jar
)

echo.
echo ========================================
echo Application has exited
echo ========================================
pause
