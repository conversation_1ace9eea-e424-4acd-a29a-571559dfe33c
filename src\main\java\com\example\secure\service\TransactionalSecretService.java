package com.example.secure.service;

import com.example.secure.dao.TestDao;
import com.example.secure.entity.TestEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 事务性敏感服务 - 测试加密类的事务支持
 *
 * 这个加密类演示了：
 * 1. @Transactional注解的正常工作
 * 2. 事务回滚机制
 * 3. 事务传播行为
 */
@Slf4j
@Service
public class TransactionalSecretService {
    
    @Autowired
    private SecretLogicService secretLogicService;

    @Autowired
    private AdvancedSecretService advancedSecretService;

    @Autowired
    private TestDao testDao;
    
    /**
     * 事务性方法 - 正常执行
     */
    @Transactional
    public String executeTransactionalOperation(String data) {
        log.info("🔄 [TRANSACTION] Starting transactional operation");
        
        try {
            // 执行一系列操作
            String config = secretLogicService.getSecretConfig();
            String calculation = secretLogicService.performSecretCalculation();
            String processed = secretLogicService.processSensitiveData(data);
            
            // 真实数据库操作
            TestEntity entity = createTestEntity(data, processed);
            int insertResult = testDao.insert(entity);
            log.info("💾 [DB-INSERT] Inserted entity with ID: {}", entity.getId());

            // 更新操作
            entity.setUserName(processed + "_UPDATED");
            entity.setUpdateTime(LocalDateTime.now());
            int updateResult = testDao.update(entity);
            log.info("💾 [DB-UPDATE] Updated entity, affected rows: {}", updateResult);
            
            String result = String.format("Transactional operation completed: config=%s, calc=%s, processed=%s", 
                                        config, calculation, processed);
            
            log.info("✅ [TRANSACTION] Operation completed successfully");
            return result;
            
        } catch (Exception e) {
            log.error("❌ [TRANSACTION] Operation failed: {}", e.getMessage(), e);
            throw e; // 触发事务回滚
        }
    }
    
    /**
     * 事务性方法 - 模拟异常回滚
     */
    @Transactional
    public String executeTransactionalOperationWithRollback(String data, boolean shouldFail) {
        log.info("🔄 [TRANSACTION-ROLLBACK] Starting transactional operation (shouldFail={})", shouldFail);
        
        try {
            // 执行一些操作
            String processed = secretLogicService.processSensitiveData(data);
            // TestEntity entity = createTestEntity(data, processed);
            // testDao.insert(entity);
            // log.info("💾 [DB-INSERT] Inserted entity for rollback test with ID: {}", entity.getId());

            if (shouldFail) {
                log.warn("💥 [TRANSACTION-ROLLBACK] Simulating failure...");
                throw new RuntimeException("Simulated transaction failure");
            }

            // 更新操作
            // entity.setUserName(processed + "_FINAL");
            // entity.setUpdateTime(LocalDateTime.now());
            // testDao.update(entity);
            // log.info("💾 [DB-UPDATE] Updated entity for rollback test");
            
            log.info("✅ [TRANSACTION-ROLLBACK] Operation completed successfully");
            return "Transaction completed: " + processed;
            
        } catch (Exception e) {
            log.warn("🔙 [TRANSACTION-ROLLBACK] Transaction will be rolled back: {}", e.getMessage());
            throw e;
        }
    }
    
    /**
     * 嵌套事务方法
     */
    @Transactional
    public String executeNestedTransactionalOperation(String data) {
        log.info("🔄 [NESTED-TRANSACTION] Starting nested transactional operation");
        
        try {
            // 调用其他事务性方法
            String result1 = executeTransactionalOperation(data + "_PART1");
            
            // 调用高级服务
            String result2 = advancedSecretService.executeAdvancedOperation(data + "_PART2");
            
            // 批量数据操作
            // TestEntity batchEntity = createTestEntity(data, "BATCH_OPERATION");
            // testDao.insert(batchEntity);
            // log.info("💾 [DB-BATCH] Inserted batch entity with ID: {}", batchEntity.getId());
            
            String finalResult = "Nested transaction result: " + result1 + " | " + result2;
            log.info("✅ [NESTED-TRANSACTION] Nested operation completed");
            
            return finalResult;
            
        } catch (Exception e) {
            log.error("❌ [NESTED-TRANSACTION] Nested operation failed: {}", e.getMessage(), e);
            throw e;
        }
    }
    
    /**
     * 只读事务方法
     */
    @Transactional(readOnly = true)
    public String executeReadOnlyOperation(String query) {
        log.info("📖 [READ-ONLY-TRANSACTION] Starting read-only operation");
        
        try {
            // 只读操作
            String config = secretLogicService.getSecretConfig();
            boolean permission = secretLogicService.validateUserPermission("readonly_user", "READ_" + query);
            
            // 真实数据查询
            // List<TestEntity> allEntities = testDao.selectAll();
            // String queryResult = "Found " + allEntities.size() + " entities";
            String queryResult = "Database query disabled";
            log.info("📋 [DB-QUERY] Query result: {}", queryResult);

            String result = String.format("Read-only result: config=%s, permission=%s, query=%s",
                                        config, permission, queryResult);
            
            log.info("✅ [READ-ONLY-TRANSACTION] Read-only operation completed");
            return result;
            
        } catch (Exception e) {
            log.error("❌ [READ-ONLY-TRANSACTION] Read-only operation failed: {}", e.getMessage(), e);
            throw e;
        }
    }
    
    /**
     * 创建测试实体
     */
    private TestEntity createTestEntity(String data, String processed) {
        return TestEntity.builder()
                .contractId((int) (Math.random() * 1000))
                .version(1)
                .tokenId((int) (Math.random() * 10000))
                .userAddress("0x" + data.hashCode())
                .userName(processed)
                .createTime(LocalDateTime.now())
                .deleted(0)
                .updateTime(LocalDateTime.now())
                .build();
    }

    /**
     * 测试数据库查询操作
     */
    @Transactional(readOnly = true)
    public List<TestEntity> testDatabaseQuery() {
        log.info("🔍 [TEST-QUERY] Starting database query test");

        try {
            // List<TestEntity> entities = testDao.selectAll();
            // log.info("📋 [TEST-QUERY] Found {} entities", entities.size());
            // return entities;
            log.info("📋 [TEST-QUERY] Database query disabled");
            return new ArrayList<>();
        } catch (Exception e) {
            log.error("❌ [TEST-QUERY] Query failed: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 测试数据库插入操作
     */
    @Transactional
    public TestEntity testDatabaseInsert(String testData) {
        log.info("💾 [TEST-INSERT] Starting database insert test");

        try {
            TestEntity entity = createTestEntity(testData, "TEST_INSERT_" + testData);
            // testDao.insert(entity);
            // log.info("✅ [TEST-INSERT] Inserted entity with ID: {}", entity.getId());
            log.info("✅ [TEST-INSERT] Database insert disabled");
            return entity;
        } catch (Exception e) {
            log.error("❌ [TEST-INSERT] Insert failed: {}", e.getMessage(), e);
            throw e;
        }
    }
}
